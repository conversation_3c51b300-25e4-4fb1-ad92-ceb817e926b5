{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\utils\\auth.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\utils\\auth.js", "mtime": 1750926936874}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IENvb2tpZXMgZnJvbSAnanMtY29va2llJzsKdmFyIFRva2VuS2V5ID0gJ3Z1ZV90b2tlbic7CmV4cG9ydCBmdW5jdGlvbiBnZXRUb2tlbigpIHsKICByZXR1cm4gQ29va2llcy5nZXQoVG9rZW5LZXkpOwp9CmV4cG9ydCBmdW5jdGlvbiBzZXRUb2tlbih0b2tlbikgewogIHJldHVybiBDb29raWVzLnNldChUb2tlbktleSwgdG9rZW4pOwp9CmV4cG9ydCBmdW5jdGlvbiByZW1vdmVUb2tlbigpIHsKICByZXR1cm4gQ29va2llcy5yZW1vdmUoVG9rZW5LZXkpOwp9"}, {"version": 3, "names": ["Cookies", "TokenKey", "getToken", "get", "setToken", "token", "set", "removeToken", "remove"], "sources": ["F:/work/ticai/ticai-web/src/utils/auth.js"], "sourcesContent": ["import Cookies from 'js-cookie'\r\n\r\nconst TokenKey = 'vue_token'\r\n\r\nexport function getToken() {\r\n  return Cookies.get(TokenKey)\r\n}\r\n\r\nexport function setToken(token) {\r\n  return Cookies.set(To<PERSON><PERSON><PERSON>, token)\r\n}\r\n\r\nexport function removeToken() {\r\n  return Cookies.remove(TokenKey)\r\n}\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAE/B,IAAMC,QAAQ,GAAG,WAAW;AAE5B,OAAO,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAOF,OAAO,CAACG,GAAG,CAACF,QAAQ,CAAC;AAC9B;AAEA,OAAO,SAASG,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAOL,OAAO,CAACM,GAAG,CAACL,QAAQ,EAAEI,KAAK,CAAC;AACrC;AAEA,OAAO,SAASE,WAAWA,CAAA,EAAG;EAC5B,OAAOP,OAAO,CAACQ,MAAM,CAACP,QAAQ,CAAC;AACjC", "ignoreList": []}]}