{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/main/login.vue?e52f", "webpack:///F:/work/ticai/ticai-dam-mp/pages/main/login.vue?9f63", "webpack:///F:/work/ticai/ticai-dam-mp/pages/main/login.vue?2aff", "webpack:///F:/work/ticai/ticai-dam-mp/pages/main/login.vue?0887", "uni-app:///pages/main/login.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/main/login.vue?01cf", "webpack:///F:/work/ticai/ticai-dam-mp/pages/main/login.vue?0965"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "canIUseGetUserProfile", "url", "openId", "wechatshareopenid", "info", "onLoad", "qstring", "methods", "getUserProfile", "desc", "success", "that", "bindGetUserInfo", "title", "content", "showCancel", "confirmText", "autoLoginReg", "jscode", "opr", "name", "avatarUrl", "province", "city", "scene", "ctx", "console", "fail", "loginClose"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACoBjzB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;MACA;MACA;MACA;MACA;QACA;UACA,4DACAC;QACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAd;QACAe;QACAC;UACA;UACAC;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;QACA;QACAlB;UACAmB;UACAC;UACAC;UACAC;UACAN;YACA;cACA;YAAA;UAEA;QACA;MACA;IACA;IACAO;MACA;MACAvB;QACAgB;UACA;YACAQ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;cAAAC;cAAAD;cAAAE;cAAAC;cAAAC;cAAAC;cAAAC;YAAA;;YAEA;YACA9B;cAAAmB;YAAA;YAEAY;cACA/B;cACA;cACAA;cACAA;cACA+B;YACA;cACA/B;cACAgC;YACA;UACA;YACAA;UACA;QACA;QACAC;UACAD;QACA;MACA;IACA;IACAE;MACAH;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzHA;AAAA;AAAA;AAAA;AAA2nC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA/oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/main/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/main/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=920ff968&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/main/login.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=template&id=920ff968&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view>\r\n\t\t\t<view class=\"wechatapp\">\r\n\t\t\t\t<view class=\"header-avatar\">\r\n\t\t\t\t\t<open-data type=\"userAvatarUrl\"></open-data>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"auth-title\">申请获取以下权限</view>\r\n\t\t\t<view class=\"auth-subtitle\">获得你的公开信息（昵称、头像等）</view>\r\n\t\t\t<button v-if=\"canIUseGetUserProfile\" class=\"login-btn\" openType=\"getUserProfile\" lang=\"zh_CN\"\r\n\t\t\t\t@click=\"getUserProfile\">{{ info }}授权登录</button>\r\n\t\t\t<button v-else class=\"login-btn\" openType=\"getUserInfo\" lang=\"zh_CN\" @click=\"bindGetUserInfo\">{{ info\r\n\t\t\t}}授权登录</button>\r\n\t\t\t<button class=\"login-close\" lang=\"zh_CN\" @tap=\"loginClose\">取消登录</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport * as ctx from '../../utils/context.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcanIUseGetUserProfile: false,\r\n\t\t\turl: '/pages/home/<USER>',\r\n\t\t\topenId: '',\r\n\t\t\twechatshareopenid: '',\r\n\t\t\tinfo: ''\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tif (wx.getUserProfile) {\r\n\t\t\tthis.canIUseGetUserProfile = true\r\n\t\t}\r\n\t\tif (options.url) {\r\n\t\t\tlet url = options.url\r\n\t\t\tif (url.substring(0, 1) != '/') url = '/' + url\r\n\t\t\tvar qstring\r\n\t\t\tfor (let obj in options) {\r\n\t\t\t\tif (obj != \"url\") {\r\n\t\t\t\t\tif (qstring) qstring += '&' + obj + \"=\" + options[obj]\r\n\t\t\t\t\telse qstring = '?' + obj + \"=\" + options[obj]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (qstring) url += qstring\r\n\t\t\tthis.url = url\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetUserProfile() {\r\n\t\t\tconst that = this;\r\n\t\t\twx.getUserProfile({\r\n\t\t\t\tdesc: '用于关联业务数据',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t//用户按了允许授权按钮\r\n\t\t\t\t\tthat.autoLoginReg(that, res.userInfo)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tbindGetUserInfo: function (e) {\r\n\t\t\tif (e.detail.userInfo) {\r\n\t\t\t\t//用户按了允许授权按钮\r\n\t\t\t\t// wx.setStorageSync(app.STORE_WX_USER, ui);//wxUser\r\n\t\t\t\tthis.autoLoginReg(this, e.detail.UserInfo)\r\n\t\t\t} else {\r\n\t\t\t\t//用户按了拒绝按钮\r\n\t\t\t\twx.showModal({\r\n\t\t\t\t\ttitle: '警告',\r\n\t\t\t\t\tcontent: '您点击了拒绝授权，将无法进入小程序，请授权之后再进入!!!',\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: '返回授权',\r\n\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t//console.log('用户点击了“返回授权”')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tautoLoginReg: function (that, ui) {\r\n\t\t\tlet jscode = '';\r\n\t\t\twx.login({\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.code) {\r\n\t\t\t\t\t\tjscode = res.code;\r\n\t\t\t\t\t\t//2、调用获取用户信息接口\r\n\t\t\t\t\t\t// wx.getUserInfo({\r\n\t\t\t\t\t\t//   success: function (res) {\r\n\t\t\t\t\t\t//     encryptedData: res.encryptedData;\r\n\t\t\t\t\t\t//     iv:res.iv;\r\n\t\t\t\t\t\t// var data = { opr: \"getcode\", jscode: jscode, name: ui.nickName, avatarUrl: ui.avatarUrl, province: ui.province, city: ui.city, encryptedData: res.encryptedData, iv: res.iv, scene: wx.getStorageSync('scene') }\r\n\t\t\t\t\t\tconst data = { opr: \"getcode\", jscode: jscode, name: ui.nickName, avatarUrl: ui.avatarUrl, province: ui.province, city: ui.city, scene: wx.getStorageSync('scene') }\r\n\r\n\t\t\t\t\t\t// 显示统一的加载提示\r\n\t\t\t\t\t\twx.showLoading({ title: '登录中...' })\r\n\r\n\t\t\t\t\t\tctx.post('/wx/login', data, function (r) {\r\n\t\t\t\t\t\t\twx.hideLoading()\r\n\t\t\t\t\t\t\tif (r.code < 0 || !r.data || !r.data.token) return ctx.error(r.msg || '登录失败，请重新尝试');\r\n\t\t\t\t\t\t\twx.setStorageSync('USER', r.data);\r\n\t\t\t\t\t\t\twx.setStorageSync('ZY_TOKEN', r.data.token);\r\n\t\t\t\t\t\t\tctx.redirect(that.url);\r\n\t\t\t\t\t\t}, function (r) {\r\n\t\t\t\t\t\t\twx.hideLoading()\r\n\t\t\t\t\t\t\tconsole.log(r)\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('获取用户登录失败！' + res.errMsg);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: function (res) {\r\n\t\t\t\t\tconsole.log('wx.login失败：', res);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tloginClose: function () {\r\n\t\t\tctx.redirect(\"/pages/tabbar/home/<USER>\")\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\tpadding: 0 60rpx;\r\n\tbackground: #fff;\r\n}\r\n\r\n.wechatapp {\r\n\tpadding: 80rpx 0 48rpx;\r\n\tborder-bottom: 1rpx solid #e3e3e3;\r\n\tmargin-bottom: 72rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.wechatapp .header-avatar {\r\n\twidth: 250rpx;\r\n\theight: 250rpx;\r\n\tborder: 2px solid #fff;\r\n\tmargin: 0 auto;\r\n\tpadding: 0;\r\n\tborder-radius: 50%;\r\n\toverflow: hidden;\r\n\tbox-shadow: 1px 0px 5px rgba(50, 50, 50, 0.3);\r\n}\r\n\r\n.auth-title {\r\n\tcolor: #585858;\r\n\tfont-size: 40rpx;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.auth-subtitle {\r\n\tcolor: #888;\r\n\tmargin-bottom: 88rpx;\r\n}\r\n\r\n.login-btn {\r\n\tborder: none;\r\n\theight: 88rpx;\r\n\tline-height: 88rpx;\r\n\tpadding: 0;\r\n\tbackground: #04be01;\r\n\tcolor: #fff;\r\n\tborder-radius: 999rpx;\r\n}\r\n\r\n.login-btn::after {\r\n\tdisplay: none;\r\n}\r\n\r\n.login-btn.button-hover {\r\n\tbox-shadow: inset 0 5rpx 30rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.login-close {\r\n\tborder: none;\r\n\theight: 88rpx;\r\n\tline-height: 88rpx;\r\n\tpadding: 0;\r\n\tbackground: #989898;\r\n\tcolor: #fff;\r\n\tborder-radius: 999rpx;\r\n\tmargin-top: 10px;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1750989846672\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}