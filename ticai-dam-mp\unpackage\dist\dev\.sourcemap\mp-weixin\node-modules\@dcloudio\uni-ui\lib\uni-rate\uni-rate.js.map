{"version": 3, "sources": ["webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue?a681", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue?2d3f", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue?af41", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue?a02f", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue?ef91", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue?0762"], "names": ["name", "props", "isFill", "type", "default", "color", "activeColor", "disabledColor", "size", "value", "modelValue", "max", "margin", "disabled", "readonly", "allowHalf", "touchable", "data", "valueSync", "userMouseFristMove", "userRated", "userLastRate", "watch", "computed", "stars", "starList", "activeWitch", "marginNumber", "created", "mounted", "setTimeout", "methods", "touchstart", "e", "clientX", "screenX", "touchmove", "mousedown", "mousemove", "mouseleave", "_getRateCount", "index", "_onChange", "_getSize", "uni", "in", "select", "boundingClientRect", "exec"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAgyB,CAAgB,gzBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4BpzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,eAmBA;EACAA;EACAC;IACAC;MACA;MACAC;MACAC;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACAE;MACA;MACAH;MACAC;IACA;IACAG;MACA;MACAJ;MACAC;IACA;IACAI;MACA;MACAL;MACAC;IACA;IACAK;MACA;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACA;MACAV;MACAC;IACA;IACAU;MACA;MACAX;MACAC;IACA;IACAW;MACA;MACAZ;MACAC;IACA;IACAY;MACA;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAb;MACA;IACA;IACAC;MACA;IACA;EACA;EACAa;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;YACAC;UACA;QACA;UACAD;YACAC;UACA;QACA;UACAD;YACAC;UACA;QACA;MACA;MACA;IACA;IAEAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IAAA;IACAC;MACA;IACA;EAIA;EACAC;IACAC;MAIA;MACA,yBAGAC;QAFAC;QACAC;MAEA;MACA;IACA;IACAC;MAIA;MACA,0BAGAH;QAFAC;QACAC;MAEA;IACA;IAEA;AACA;AACA;IAEAE,kCAWA;IACAC,kCAeA;IACAC,oCAUA;IAgBA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;MACA;QACA;UACAhC;QACA;UACAA;QACA;MACA;QACAA;MACA;MAEAA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAiC;MAEA;MACA;MACA;QACAjC;MACA;IACA;IACA;AACA;AACA;IACAkC;MAAA;MAEAC,0BACAC,SACAC,oBACAC,qBACAC;QACA;UACA;QACA;MACA;IAUA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpUA;AAAA;AAAA;AAAA;AAAm+C,CAAgB,u7CAAG,EAAC,C;;;;;;;;;;;ACAv/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-rate.vue?vue&type=template&id=290762a6&\"\nvar renderjs\nimport script from \"./uni-rate.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-rate.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-rate.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-rate/uni-rate.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-rate.vue?vue&type=template&id=290762a6&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-rate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-rate.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view ref=\"uni-rate\" class=\"uni-rate\">\r\n\t\t\t<view class=\"uni-rate__icon\" :class=\"{'uni-cursor-not-allowed': disabled}\"\r\n\t\t\t\t:style=\"{ 'margin-right': marginNumber + 'px' }\" v-for=\"(star, index) in stars\" :key=\"index\"\r\n\t\t\t\*****************=\"touchstart\" @touchmove.stop=\"touchmove\" @mousedown.stop=\"mousedown\"\r\n\t\t\t\****************=\"mousemove\" @mouseleave=\"mouseleave\">\r\n\t\t\t\t<uni-icons :color=\"color\" :size=\"size\" :type=\"isFill ? 'star-filled' : 'star'\" />\r\n\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t<view :style=\"{ width: star.activeWitch.replace('%','')*size/100+'px'}\" class=\"uni-rate__icon-on\">\r\n\t\t\t\t\t<uni-icons style=\"text-align: left;\" :color=\"disabled?'#ccc':activeColor\" :size=\"size\"\r\n\t\t\t\t\t\ttype=\"star-filled\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t<view :style=\"{ width: star.activeWitch}\" class=\"uni-rate__icon-on\">\r\n\t\t\t\t\t<uni-icons :color=\"disabled?disabledColor:activeColor\" :size=\"size\" type=\"star-filled\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef APP-NVUE\r\n\tconst dom = uni.requireNativePlugin('dom');\r\n\t// #endif\r\n\t/**\r\n\t * Rate 评分\r\n\t * @description 评分组件\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=33\r\n\t * @property {Boolean} \tisFill = [true|false] \t\t星星的类型，是否为实心类型, 默认为实心\r\n\t * @property {String} \tcolor \t\t\t\t\t\t未选中状态的星星颜色，默认为 \"#ececec\"\r\n\t * @property {String} \tactiveColor \t\t\t\t选中状态的星星颜色，默认为 \"#ffca3e\"\r\n\t * @property {String} \tdisabledColor \t\t\t\t禁用状态的星星颜色，默认为 \"#c0c0c0\"\r\n\t * @property {Number} \tsize \t\t\t\t\t\t星星的大小\r\n\t * @property {Number} \tvalue/v-model \t\t\t\t当前评分\r\n\t * @property {Number} \tmax \t\t\t\t\t\t最大评分评分数量，目前一分一颗星\r\n\t * @property {Number} \tmargin \t\t\t\t\t\t星星的间距，单位 px\r\n\t * @property {Boolean} \tdisabled = [true|false] \t是否为禁用状态，默认为 false\r\n\t * @property {Boolean} \treadonly = [true|false] \t是否为只读状态，默认为 false\r\n\t * @property {Boolean} \tallowHalf = [true|false] \t是否实现半星，默认为 false\r\n\t * @property {Boolean} \ttouchable = [true|false] \t是否支持滑动手势，默认为 true\r\n\t * @event {Function} change \t\t\t\t\t\tuniRate 的 value 改变时触发事件，e={value:Number}\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: \"UniRate\",\r\n\t\tprops: {\r\n\t\t\tisFill: {\r\n\t\t\t\t// 星星的类型，是否镂空\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\t// 星星未选中的颜色\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#ececec\"\r\n\t\t\t},\r\n\t\t\tactiveColor: {\r\n\t\t\t\t// 星星选中状态颜色\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#ffca3e\"\r\n\t\t\t},\r\n\t\t\tdisabledColor: {\r\n\t\t\t\t// 星星禁用状态颜色\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"#c0c0c0\"\r\n\t\t\t},\r\n\t\t\tsize: {\r\n\t\t\t\t// 星星的大小\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 24\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\t// 当前评分\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tmodelValue: {\r\n\t\t\t\t// 当前评分\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tmax: {\r\n\t\t\t\t// 最大评分\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 5\r\n\t\t\t},\r\n\t\t\tmargin: {\r\n\t\t\t\t// 星星的间距\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tdisabled: {\r\n\t\t\t\t// 是否可点击\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\treadonly: {\r\n\t\t\t\t// 是否只读\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tallowHalf: {\r\n\t\t\t\t// 是否显示半星\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\ttouchable: {\r\n\t\t\t\t// 是否支持滑动手势\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tvalueSync: \"\",\r\n\t\t\t\tuserMouseFristMove: true,\r\n\t\t\t\tuserRated: false,\r\n\t\t\t\tuserLastRate: 1\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue(newVal) {\r\n\t\t\t\tthis.valueSync = Number(newVal);\r\n\t\t\t},\r\n\t\t\tmodelValue(newVal) {\r\n\t\t\t\tthis.valueSync = Number(newVal);\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tstars() {\r\n\t\t\t\tconst value = this.valueSync ? this.valueSync : 0;\r\n\t\t\t\tconst starList = [];\r\n\t\t\t\tconst floorValue = Math.floor(value);\r\n\t\t\t\tconst ceilValue = Math.ceil(value);\r\n\t\t\t\tfor (let i = 0; i < this.max; i++) {\r\n\t\t\t\t\tif (floorValue > i) {\r\n\t\t\t\t\t\tstarList.push({\r\n\t\t\t\t\t\t\tactiveWitch: \"100%\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else if (ceilValue - 1 === i) {\r\n\t\t\t\t\t\tstarList.push({\r\n\t\t\t\t\t\t\tactiveWitch: (value - floorValue) * 100 + \"%\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tstarList.push({\r\n\t\t\t\t\t\t\tactiveWitch: \"0\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn starList;\r\n\t\t\t},\r\n\r\n\t\t\tmarginNumber() {\r\n\t\t\t\treturn Number(this.margin)\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.valueSync = Number(this.value || this.modelValue);\r\n\t\t\tthis._rateBoxLeft = 0\r\n\t\t\tthis._oldValue = null\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis._getSize()\r\n\t\t\t}, 100)\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.PC = this.IsPC()\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttouchstart(e) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (this.IsPC()) return\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (this.readonly || this.disabled) return\r\n\t\t\t\tconst {\r\n\t\t\t\t\tclientX,\r\n\t\t\t\t\tscreenX\r\n\t\t\t\t} = e.changedTouches[0]\r\n\t\t\t\t// TODO 做一下兼容，只有 Nvue 下才有 screenX，其他平台式 clientX\r\n\t\t\t\tthis._getRateCount(clientX || screenX)\r\n\t\t\t},\r\n\t\t\ttouchmove(e) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (this.IsPC()) return\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (this.readonly || this.disabled || !this.touchable) return\r\n\t\t\t\tconst {\r\n\t\t\t\t\tclientX,\r\n\t\t\t\t\tscreenX\r\n\t\t\t\t} = e.changedTouches[0]\r\n\t\t\t\tthis._getRateCount(clientX || screenX)\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 兼容 PC @tian\r\n\t\t\t */\r\n\r\n\t\t\tmousedown(e) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!this.IsPC()) return\r\n\t\t\t\tif (this.readonly || this.disabled) return\r\n\t\t\t\tconst {\r\n\t\t\t\t\tclientX,\r\n\t\t\t\t} = e\r\n\t\t\t\tthis.userLastRate = this.valueSync\r\n\t\t\t\tthis._getRateCount(clientX)\r\n\t\t\t\tthis.userRated = true\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tmousemove(e) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!this.IsPC()) return\r\n\t\t\t\tif (this.userRated) return\r\n\t\t\t\tif (this.userMouseFristMove) {\r\n\t\t\t\t\tconsole.log('---mousemove----', this.valueSync);\r\n\t\t\t\t\tthis.userLastRate = this.valueSync\r\n\t\t\t\t\tthis.userMouseFristMove = false\r\n\t\t\t\t}\r\n\t\t\t\tif (this.readonly || this.disabled || !this.touchable) return\r\n\t\t\t\tconst {\r\n\t\t\t\t\tclientX,\r\n\t\t\t\t} = e\r\n\t\t\t\tthis._getRateCount(clientX)\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tmouseleave(e) {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!this.IsPC()) return\r\n\t\t\t\tif (this.readonly || this.disabled || !this.touchable) return\r\n\t\t\t\tif (this.userRated) {\r\n\t\t\t\t\tthis.userRated = false\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.valueSync = this.userLastRate\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// #ifdef H5\r\n\t\t\tIsPC() {\r\n\t\t\t\tvar userAgentInfo = navigator.userAgent;\r\n\t\t\t\tvar Agents = [\"Android\", \"iPhone\", \"SymbianOS\", \"Windows Phone\", \"iPad\", \"iPod\"];\r\n\t\t\t\tvar flag = true;\r\n\t\t\t\tfor (let v = 0; v < Agents.length - 1; v++) {\r\n\t\t\t\t\tif (userAgentInfo.indexOf(Agents[v]) > 0) {\r\n\t\t\t\t\t\tflag = false;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn flag;\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\r\n\t\t\t/**\r\n\t\t\t * 获取星星个数\r\n\t\t\t */\r\n\t\t\t_getRateCount(clientX) {\r\n\t\t\t\tthis._getSize()\r\n\t\t\t\tconst size = Number(this.size)\r\n\t\t\t\tif (isNaN(size)) {\r\n\t\t\t\t\treturn new Error('size 属性只能设置为数字')\r\n\t\t\t\t}\r\n\t\t\t\tconst rateMoveRange = clientX - this._rateBoxLeft\r\n\t\t\t\tlet index = parseInt(rateMoveRange / (size + this.marginNumber))\r\n\t\t\t\tindex = index < 0 ? 0 : index;\r\n\t\t\t\tindex = index > this.max ? this.max : index;\r\n\t\t\t\tconst range = parseInt(rateMoveRange - (size + this.marginNumber) * index);\r\n\t\t\t\tlet value = 0;\r\n\t\t\t\tif (this._oldValue === index && !this.PC) return;\r\n\t\t\t\tthis._oldValue = index;\r\n\t\t\t\tif (this.allowHalf) {\r\n\t\t\t\t\tif (range > (size / 2)) {\r\n\t\t\t\t\t\tvalue = index + 1\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvalue = index + 0.5\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvalue = index + 1\r\n\t\t\t\t}\r\n\r\n\t\t\t\tvalue = Math.max(0.5, Math.min(value, this.max))\r\n\t\t\t\tthis.valueSync = value\r\n\t\t\t\tthis._onChange()\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 触发动态修改\r\n\t\t\t */\r\n\t\t\t_onChange() {\r\n\r\n\t\t\t\tthis.$emit(\"input\", this.valueSync);\r\n\t\t\t\tthis.$emit(\"update:modelValue\", this.valueSync);\r\n\t\t\t\tthis.$emit(\"change\", {\r\n\t\t\t\t\tvalue: this.valueSync\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取星星距离屏幕左侧距离\r\n\t\t\t */\r\n\t\t\t_getSize() {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tuni.createSelectorQuery()\r\n\t\t\t\t\t.in(this)\r\n\t\t\t\t\t.select('.uni-rate')\r\n\t\t\t\t\t.boundingClientRect()\r\n\t\t\t\t\t.exec(ret => {\r\n\t\t\t\t\t\tif (ret) {\r\n\t\t\t\t\t\t\tthis._rateBoxLeft = ret[0].left\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tdom.getComponentRect(this.$refs['uni-rate'], (ret) => {\r\n\t\t\t\t\tconst size = ret.size\r\n\t\t\t\t\tif (size) {\r\n\t\t\t\t\t\tthis._rateBoxLeft = size.left\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.uni-rate {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tline-height: 1;\r\n\t\tfont-size: 0;\r\n\t\tflex-direction: row;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-rate__icon {\r\n\t\tposition: relative;\r\n\t\tline-height: 1;\r\n\t\tfont-size: 0;\r\n\t}\r\n\r\n\t.uni-rate__icon-on {\r\n\t\toverflow: hidden;\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tline-height: 1;\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.uni-cursor-not-allowed {\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: not-allowed !important;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-rate.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-rate.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1750989851725\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}