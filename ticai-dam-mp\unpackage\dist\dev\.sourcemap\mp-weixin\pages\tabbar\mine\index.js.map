{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/mine/index.vue?dd9c", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/mine/index.vue?329b", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/mine/index.vue?77c5", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/mine/index.vue?865d", "uni-app:///pages/tabbar/mine/index.vue", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/mine/index.vue?3987", "webpack:///F:/work/ticai/ticai-dam-mp/pages/tabbar/mine/index.vue?543f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "user", "formData", "account", "password", "formRules", "rules", "required", "errorMessage", "onLoad", "ctx", "that", "onShareAppMessage", "title", "desc", "path", "imageUrl", "onShareTimeline", "methods", "bindUser", "unbindUser", "submitBind", "cancelBind", "toLogout", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,6yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6CjzB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;MACAC;QACAF;UACAG;YAAAC;YAAAC;UAAA;QACA;QACAJ;UACAE;YAAAC;YAAAC;UAAA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;EACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MAAAJ;IAAA;EACA;EACAK;IACAC;MACA;IACA;IACAC;MACA;MACAV;QACA;QACA;QACAf;QACAgB;QACA;QACAD;QACAA;MACA;IACA;IACAW;MACA;MACAX;QACA;QACA;QACAf;QACAgB;QACAA;QACA;QACAD;QACAA;MACA;IACA;IACAY;MACA;IACA;IACAC;MACAb;QACA;QACAf;QACAA;UAAA6B;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxHA;AAAA;AAAA;AAAA;AAA2nC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA/oC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/tabbar/mine/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/tabbar/mine/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2a7bd3be&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/tabbar/mine/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=2a7bd3be&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-popup/uni-popup\" */ \"@dcloudio/uni-ui/lib/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms/uni-forms\" */ \"@dcloudio/uni-ui/lib/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item\" */ \"@dcloudio/uni-ui/lib/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput\" */ \"@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    watermark: function () {\n      return import(\n        /* webpackChunkName: \"components/watermark/watermark\" */ \"@/components/watermark/watermark.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"card\">\r\n\t\t\t<view class=\"thumb\">\r\n\t\t\t\t<open-data type=\"userAvatarUrl\"></open-data>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info\">\r\n\t\t\t\t<view style=\"text-align: left;\">\r\n\t\t\t\t\t姓名：{{ user.name || '***' }}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn-bar\">\r\n\t\t\t\t\t<button v-if=\"user.user == '0'\" type=\"primary\" size=\"mini\" @click=\"bindUser\">绑定用户</button>\r\n\t\t\t\t\t<button v-else type=\"warn\" size=\"mini\" @click=\"unbindUser\">取消绑定</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- <view class=\"btn-bar\">\r\n\t\t\t<button type=\"warn\" @click=\"toLogout\">退出登录</button>\r\n\t\t</view> -->\r\n\t\t<uni-popup ref=\"popup\" type=\"dialog\" background-color=\"#fff\" :mask-click=\"false\">\r\n\t\t\t<view class=\"dialog-head\">用户绑定</view>\r\n\t\t\t<view class=\"dialog-body\">\r\n\t\t\t\t<uni-forms ref=\"form\" :modelValue=\"formData\" :rules=\"formRules\">\r\n\t\t\t\t\t<uni-forms-item label=\"帐号\" name=\"account\">\r\n\t\t\t\t\t\t<uni-easyinput type=\"text\" v-model=\"formData.account\" placeholder=\"请输入登录帐号\" />\r\n\t\t\t\t\t</uni-forms-item>\r\n\t\t\t\t\t<uni-forms-item label=\"密码\" name=\"password\">\r\n\t\t\t\t\t\t<uni-easyinput type=\"password\" v-model=\"formData.password\" placeholder=\"请输入登录密码\" />\r\n\t\t\t\t\t</uni-forms-item>\r\n\t\t\t\t</uni-forms>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"dialog-foot\">\r\n\t\t\t\t<view class=\"btn-bar\">\r\n\t\t\t\t\t<button size=\"mini\" @click=\"cancelBind\">取 消</button>\r\n\t\t\t\t\t<button type=\"primary\" size=\"mini\" @click=\"submitBind\">提 交</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</uni-popup>\r\n\r\n\t\t<!-- 水印组件 -->\r\n\t\t<watermark />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport * as ctx from '../../../utils/context.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tuser: {},\r\n\t\t\tformData: { account: null, password: null },\r\n\t\t\tformRules: {\r\n\t\t\t\taccount: {\r\n\t\t\t\t\trules: [{ required: true, errorMessage: '请输入登录帐号' }]\r\n\t\t\t\t},\r\n\t\t\t\tpassword: {\r\n\t\t\t\t\trules: [{ required: true, errorMessage: '请输入登录密码' }]\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tconst that = this\r\n\t\tctx.checkLogin(function (user) {\r\n\t\t\tthat.user = user\r\n\t\t}, false);\r\n\t},\r\n\t//分享\r\n\tonShareAppMessage: function (obj) {\r\n\t\treturn {\r\n\t\t\ttitle: '资产管理平台',\r\n\t\t\tdesc: '',\r\n\t\t\tpath: '/pages/home/<USER>',\r\n\t\t\timageUrl: '/static/logo.png'\r\n\t\t};\r\n\t},\r\n\tonShareTimeline() {\r\n\t\treturn { title: '资产管理平台' }\r\n\t},\r\n\tmethods: {\r\n\t\tbindUser() {\r\n\t\t\tthis.$refs.popup.open()\r\n\t\t},\r\n\t\tunbindUser() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/unbind', function (r) {\r\n\t\t\t\tif (r.code < 0 || !r.data) return ctx.error(r.msg || '取消失败');\r\n\t\t\t\tconst user = r.data;\r\n\t\t\t\twx.setStorageSync(\"USER\", user);\r\n\t\t\t\tthat.user = user\r\n\t\t\t\t// 清理登录状态缓存\r\n\t\t\t\tctx.clearLoginCache();\r\n\t\t\t\tctx.ok('取消成功')\r\n\t\t\t});\r\n\t\t},\r\n\t\tsubmitBind() {\r\n\t\t\tconst that = this\r\n\t\t\tctx.post('/wx/bind', this.formData, function (r) {\r\n\t\t\t\tif (r.code < 0 || !r.data) return ctx.error(r.msg || '提交失败');\r\n\t\t\t\tconst user = r.data;\r\n\t\t\t\twx.setStorageSync(\"USER\", user);\r\n\t\t\t\tthat.user = user\r\n\t\t\t\tthat.$refs.popup.close()\r\n\t\t\t\t// 清理登录状态缓存\r\n\t\t\t\tctx.clearLoginCache();\r\n\t\t\t\tctx.ok('绑定成功')\r\n\t\t\t});\r\n\t\t},\r\n\t\tcancelBind() {\r\n\t\t\tthis.$refs.popup.close()\r\n\t\t},\r\n\t\ttoLogout() {\r\n\t\t\tctx.post('/wx/logout', function (res) {\r\n\t\t\t\tif (res.code < 0) ctx.error(res.msg || '退出登录失败')\r\n\t\t\t\twx.removeStorageSync(\"USER\")\r\n\t\t\t\twx.switchTab({ url: '/pages/tabbar/home/<USER>' })\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.container {\r\n\ttext-align: center;\r\n\tmargin-top: 40upx;\r\n}\r\n\r\n.card {\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n}\r\n\r\n.card .thumb {\r\n\twidth: 200rpx;\r\n\theight: 200rpx;\r\n\tmargin: 20rpx;\r\n}\r\n\r\n.card .info {\r\n\tflex: 1;\r\n\tmargin: 20rpx;\r\n\tpadding-left: 40rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1750989846663\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}