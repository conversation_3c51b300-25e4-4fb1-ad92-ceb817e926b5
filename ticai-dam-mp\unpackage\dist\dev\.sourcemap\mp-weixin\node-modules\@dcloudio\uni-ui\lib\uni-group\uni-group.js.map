{"version": 3, "sources": ["webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-group/uni-group.vue?9f83", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-group/uni-group.vue?003b", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-group/uni-group.vue?ab4f", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-group/uni-group.vue?aaff", "uni-app:///node_modules/@dcloudio/uni-ui/lib/uni-group/uni-group.vue", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-group/uni-group.vue?49ad", "webpack:///F:/work/ticai/ticai-dam-mp/node_modules/@dcloudio/uni-ui/lib/uni-group/uni-group.vue?51af"], "names": ["name", "emits", "props", "title", "type", "default", "top", "mode", "stat", "data", "margin", "border", "watch", "uni", "created", "methods", "getForm", "parent", "parentName", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC2M;AAC3M,gBAAgB,mNAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAiyB,CAAgB,izBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACcrzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,eAQA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAT;MACA;QACAU;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACAC;QACA;QACAC;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjFA;AAAA;AAAA;AAAA;AAAo+C,CAAgB,w7CAAG,EAAC,C;;;;;;;;;;;ACAx/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/@dcloudio/uni-ui/lib/uni-group/uni-group.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-group.vue?vue&type=template&id=854b19e8&\"\nvar renderjs\nimport script from \"./uni-group.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-group.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-group.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/@dcloudio/uni-ui/lib/uni-group/uni-group.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-group.vue?vue&type=template&id=854b19e8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-group.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-group.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-group\" :class=\"['uni-group--'+mode ,margin?'group-margin':'']\" :style=\"{marginTop: `${top}px` }\">\r\n\t\t<slot name=\"title\">\r\n\t\t\t<view v-if=\"title\" class=\"uni-group__title\" :style=\"{'padding-left':border?'30px':'15px'}\">\r\n\t\t\t\t<text class=\"uni-group__title-text\">{{ title }}</text>\r\n\t\t\t</view>\r\n\t\t</slot>\r\n\t\t<view class=\"uni-group__content\" :class=\"{'group-conent-padding':border}\">\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * Group 分组\r\n\t * @description 表单字段分组\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=3281\r\n\t * @property {String} title 主标题\r\n\t * @property {Number} top 分组间隔\r\n\t * @property {Number} mode 模式\r\n\t */\r\n\texport default {\r\n\t\tname: 'uniGroup',\r\n\t\temits:['click'],\r\n\t\tprops: {\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ttop: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 10\r\n\t\t\t},\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\n\t\t\tstat:{\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmargin: false,\r\n\t\t\t\tborder: false\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\ttitle(newVal) {\r\n\t\t\t\tif (uni.report && this.stat && newVal !== '') {\r\n\t\t\t\t\tuni.report('title', newVal)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.form = this.getForm()\r\n\t\t\tif (this.form) {\r\n\t\t\t\tthis.margin = true\r\n\t\t\t\tthis.border = this.form.border\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 获取父元素实例\r\n\t\t\t */\r\n\t\t\tgetForm() {\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\tlet parentName = parent.$options.name;\r\n\t\t\t\twhile (parentName !== 'uniForms') {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t\tif (!parent) return false\r\n\t\t\t\t\tparentName = parent.$options.name;\r\n\t\t\t\t}\r\n\t\t\t\treturn parent;\r\n\t\t\t},\r\n\t\t\tonClick() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" >\r\n\t.uni-group {\r\n\t\tbackground: #fff;\r\n\t\tmargin-top: 10px;\r\n\t\t// border: 1px red solid;\r\n\t}\r\n\r\n\t.group-margin {\r\n\t\t// margin: 0 -15px;\r\n\t}\r\n\r\n\t.uni-group__title {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tpadding-left: 15px;\r\n\t\theight: 40px;\r\n\t\tbackground-color: #eee;\r\n\t\tfont-weight: normal;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.uni-group__content {\r\n\t\tpadding: 15px;\r\n\t\t// padding-bottom: 5px;\r\n\t\t// background-color: #FFF;\r\n\t}\r\n\r\n\t.group-conent-padding {\r\n\t\tpadding: 0 15px;\r\n\t}\r\n\r\n\t.uni-group__title-text {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.distraction {\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.uni-group--card {\r\n\t\tmargin: 10px;\r\n\t\tborder-radius: 5px;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 0 5px 1px rgba($color: #000000, $alpha: 0.08);\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-group.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-group.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1750989851846\n      var cssReload = require(\"D:/HBuilderX/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}