version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: ticai-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: @@5HisEF3j
      MYSQL_DATABASE: dam
      MYSQL_USER: root
      MYSQL_PASSWORD: XX2U5nxQYeMyQkOA!!
    ports:
      - "53306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./ticai-dam/sql:/docker-entrypoint-initdb.d
    networks:
      - ticai-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: ticai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ticai-network
    command: redis-server --appendonly yes

  # 主后端服务
  ticai-dam:
    build:
      context: ./ticai-dam
      dockerfile: Dockerfile
    container_name: ticai-dam
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**********************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=ticai
      - SPRING_DATASOURCE_PASSWORD=ticai123
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - ticai-network
    volumes:
      - ./logs:/app/logs
      - ./data/attach:/data/attach

  # 外部接口服务
  ticai-dam-port:
    build:
      context: ./ticai-dam-port
      dockerfile: Dockerfile
    container_name: ticai-dam-port
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**********************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=ticai
      - SPRING_DATASOURCE_PASSWORD=ticai123
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
      - ticai-dam
    networks:
      - ticai-network
    volumes:
      - ./logs:/app/logs
      - ./data/attach:/data/attach

  # 前端 Web 应用
  ticai-web:
    build:
      context: ./ticai-web
      dockerfile: Dockerfile
    container_name: ticai-web
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - ticai-dam
      - ticai-dam-port
    networks:
      - ticai-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  ticai-network:
    driver: bridge
